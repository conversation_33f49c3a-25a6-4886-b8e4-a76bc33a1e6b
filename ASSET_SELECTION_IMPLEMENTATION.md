# Asset & Party Selection Implementation Guide

## Tổng quan
Chức năng chọn tài sản và đương sự đã đượ<PERSON> implement thành công cho wizard t<PERSON><PERSON> hồ sơ:
- **Bước 3**: Chọ<PERSON> đương sự từ template fields
- **Bước 4**: Chọn tài sản từ template fields

Người dùng có thể chọn đương sự và tài sản từ các fields của template đã được định nghĩa trước.

## Files đã tạo/chỉnh sửa

### 1. Backend Files

#### Controllers
- **app/Http/Controllers/DocumentController.php**
  - Thêm method `getTemplateAssets()` để lấy danh sách assets từ template
  - Cập nhật `storeFromWizard()` để xử lý selected assets
  - Thêm permission check cho asset selection

#### Routes
- **routes/web.php**
  - Thêm route `/documents/ajax/template-assets` cho API endpoint

#### Permissions
- **database/seeders/AssetSelectionPermissionsSeeder.php**
  - Tạo permissions mới: `assets.select`, `assets.manage-selection`, `template-assets.view`, `template-assets.select`
  - Assign permissions cho các roles hiện có

### 2. Frontend Files

#### CSS
- **resources/css/asset-selection.css**
  - Styles cho asset selection interface
  - Responsive design
  - Animation effects
  - Validation states

- **resources/css/party-selection.css**
  - Styles cho party selection interface
  - Party type indicators
  - Group-based styling
  - Responsive design

#### JavaScript
- **resources/js/asset-selection.js**
  - Core logic cho asset selection
  - AJAX calls để load assets từ template
  - Validation functions
  - Event handlers

- **resources/js/party-selection.js**
  - Core logic cho party selection
  - AJAX calls để load parties từ template
  - Party type validation
  - Event handlers

#### Views
- **resources/views/documents/wizard.blade.php**
  - Cập nhật bước 4 (assets-step) với asset selection interface
  - Thêm CSS và JS includes
  - Thêm hidden input cho validation

#### Enhanced JavaScript
- **resources/js/documents-wizard.js**
  - Tích hợp asset selection vào wizard flow
  - Thêm validation cho bước 4
  - Load assets khi template được chọn

### 3. Testing Files
- **resources/js/test-asset-selection.js**
  - Test functions cho asset selection
  - Mock data và responses
  - Automated testing capabilities

## Cách sử dụng

### 1. Chạy Seeder
```bash
php artisan db:seed --class=AssetSelectionPermissionsSeeder
```

### 2. Workflow
1. **Bước 1**: Chọn loại hợp đồng
2. **Bước 2**: Chọn template → Hệ thống load party và asset fields từ template
3. **Bước 3**: Chọn đương sự từ danh sách đã load từ template
4. **Bước 4**: Chọn tài sản từ danh sách đã load từ template
5. **Bước 5**: Review và submit

### 3. Selection Features

#### Party Selection (Bước 3)
- **Party type indicators**: Hiển thị loại đương sự (Bên A, Bên B, Người chứng kiến)
- **Required validation**: Bắt buộc có ít nhất Bên A và Bên B
- **Group display**: Parties được group theo `group_name`
- **Party type summary**: Hiển thị số lượng từng loại đương sự
- **Selection summary**: Hiển thị tóm tắt parties đã chọn

#### Asset Selection (Bước 4)
- **Hiển thị theo nhóm**: Assets được group theo `group_name`
- **Required fields**: Hiển thị badge "Bắt buộc" cho required fields
- **Field types**: Hiển thị loại field (text, number, date, etc.)
- **Validation**: Kiểm tra required fields và ít nhất 1 asset được chọn
- **Selection summary**: Hiển thị tóm tắt assets đã chọn
- **Bulk actions**: Chọn tất cả trong nhóm, bỏ chọn tất cả

## API Endpoints

### GET /documents/ajax/template-assets
**Parameters:**
- `template_id`: ID của template

**Response:**
```json
{
  "success": true,
  "template": {
    "id": 1,
    "name": "Template name",
    "description": "Template description"
  },
  "asset_fields": [
    {
      "id": 1,
      "name": "field_name",
      "label": "Field Label",
      "type": "text",
      "is_required": true,
      "group_name": "Thông tin cơ bản",
      "sort_order": 1,
      "help_text": "Help text",
      "placeholder": "Placeholder text"
    }
  ],
  "grouped_fields": {
    "Thông tin cơ bản": [...],
    "Thông tin bổ sung": [...]
  }
}
```

## Permissions

### Permissions mới
- `template-assets.view`: Xem danh sách assets từ template
- `template-assets.select`: Chọn assets từ template
- `assets.select`: Chọn assets trong wizard
- `assets.manage-selection`: Quản lý asset selection

### Role Assignments
- **Super Admin**: Tất cả permissions
- **Admin**: Tất cả permissions
- **Notary Staff**: view, select permissions
- **Viewer**: Chỉ view permission

## Database Changes

### Document Storage
Selected assets được lưu trong `documents.wizard_data` và `document_assets` table với cấu trúc:

```json
{
  "field_id": 1,
  "field_name": "dia_chi",
  "field_label": "Địa chỉ",
  "field_type": "textarea",
  "group_name": "Thông tin cơ bản",
  "is_required": true
}
```

## Testing

### Manual Testing
1. Truy cập `/documents/wizard`
2. Chọn contract type và template
3. Kiểm tra bước 4 có hiển thị assets từ template
4. Test chọn/bỏ chọn assets
5. Test validation khi chuyển sang bước 5

### Automated Testing
```javascript
// Thêm ?test=asset-selection vào URL để chạy auto test
window.testAssetSelection.runTests();
```

## Troubleshooting

### Common Issues
1. **Assets không load**: Kiểm tra template có fields không
2. **Permission denied**: Kiểm tra user có đúng permissions
3. **Validation fails**: Đảm bảo chọn ít nhất 1 asset và tất cả required fields

### Debug Mode
```javascript
// Enable debug logging
window.assetSelectionDebug = true;
```

## Future Enhancements
1. **Asset preview**: Hiển thị preview của asset
2. **Custom field values**: Cho phép nhập giá trị cho fields
3. **Asset templates**: Tạo template cho assets thường dùng
4. **Bulk import**: Import assets từ file Excel/CSV
5. **Asset validation**: Validate giá trị theo field type

## Support
Liên hệ team development để được hỗ trợ khi gặp vấn đề với asset selection functionality.
