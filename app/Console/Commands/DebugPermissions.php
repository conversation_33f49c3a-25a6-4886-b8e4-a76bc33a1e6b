<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\Gate;

class DebugPermissions extends Command
{
    protected $signature = 'permission:debug {--user-email= : Email of user to debug}';
    protected $description = 'Debug permission system after Spatie migration';

    public function handle()
    {
        $this->info('🔍 DEBUGGING SPATIE PERMISSION SYSTEM');
        $this->newLine();

        // Step 1: Check config
        $this->checkConfig();
        
        // Step 2: Check database structure
        $this->checkDatabase();
        
        // Step 3: Check specific user
        $this->checkUser();
        
        // Step 4: Test permissions
        $this->testPermissions();
        
        // Step 5: Check gates
        $this->checkGates();

        $this->newLine();
        $this->info('✅ Debug completed!');
    }

    private function checkConfig()
    {
        $this->info('📋 STEP 1: CHECKING CONFIG');
        $this->line('─────────────────────────');

        $aclConfig = config('acl');
        if (!$aclConfig) {
            $this->error('❌ config/acl.php not found!');
            return;
        }

        $this->info('✅ config/acl.php exists');
        $this->line("Super Admin Role: " . config('acl.super_admin_role', 'NOT SET'));
        $this->line("Modules count: " . count($aclConfig['modules'] ?? []));
        
        $this->newLine();
    }

    private function checkDatabase()
    {
        $this->info('🗄️ STEP 2: CHECKING DATABASE');
        $this->line('─────────────────────────');

        // Check roles
        $rolesCount = Role::count();
        $this->line("Spatie Roles: {$rolesCount}");
        
        $roles = Role::all();
        foreach ($roles as $role) {
            $this->line("  - {$role->name} (guard: {$role->guard_name})");
        }

        // Check permissions
        $permissionsCount = Permission::count();
        $this->line("Spatie Permissions: {$permissionsCount}");

        // Check users with roles
        $usersWithRoles = User::whereHas('roles')->count();
        $this->line("Users with Spatie roles: {$usersWithRoles}");

        $this->newLine();
    }

    private function checkUser()
    {
        $this->info('👤 STEP 3: CHECKING USER');
        $this->line('─────────────────────────');

        $email = $this->option('user-email');
        if (!$email) {
            // Find first user with Super Admin role
            $superAdminRole = config('acl.super_admin_role', 'Super Admin');
            $user = User::whereHas('roles', function($query) use ($superAdminRole) {
                $query->where('name', $superAdminRole);
            })->first();

            if (!$user) {
                $this->error('❌ No Super Admin user found!');
                $this->line('Available users:');
                User::with('roles')->get()->each(function($u) {
                    $roles = $u->roles->pluck('name')->join(', ') ?: 'No roles';
                    $this->line("  - {$u->email} ({$roles})");
                });
                return;
            }
        } else {
            $user = User::where('email', $email)->first();
            if (!$user) {
                $this->error("❌ User with email {$email} not found!");
                return;
            }
        }

        $this->info("✅ Testing user: {$user->email}");
        
        // Check roles
        $userRoles = $user->roles->pluck('name')->toArray();
        $this->line("Roles: " . (empty($userRoles) ? 'None' : implode(', ', $userRoles)));

        // Check Super Admin status
        $isSuperAdmin = $user->isSuperAdmin();
        $this->line("Is Super Admin: " . ($isSuperAdmin ? '✅ YES' : '❌ NO'));

        // Test some permissions
        $testPermissions = ['documents.view', 'users.view', 'non.existent.permission'];
        foreach ($testPermissions as $permission) {
            $hasPermission = $user->hasPermission($permission);
            $status = $hasPermission ? '✅' : '❌';
            $this->line("  {$status} {$permission}");
        }

        $this->newLine();
    }

    private function testPermissions()
    {
        $this->info('🧪 STEP 4: TESTING PERMISSION METHODS');
        $this->line('─────────────────────────');

        $superAdminRole = config('acl.super_admin_role', 'Super Admin');
        $user = User::whereHas('roles', function($query) use ($superAdminRole) {
            $query->where('name', $superAdminRole);
        })->first();

        if (!$user) {
            $this->error('❌ No Super Admin user to test');
            return;
        }

        // Test isSuperAdmin method
        try {
            $result = $user->isSuperAdmin();
            $this->line("isSuperAdmin(): " . ($result ? '✅ true' : '❌ false'));
        } catch (\Exception $e) {
            $this->error("isSuperAdmin() error: " . $e->getMessage());
        }

        // Test hasPermission method
        try {
            $result = $user->hasPermission('documents.view');
            $this->line("hasPermission('documents.view'): " . ($result ? '✅ true' : '❌ false'));
        } catch (\Exception $e) {
            $this->error("hasPermission() error: " . $e->getMessage());
        }

        // Test Spatie methods directly
        try {
            $result = $user->hasRole($superAdminRole);
            $this->line("hasRole('{$superAdminRole}'): " . ($result ? '✅ true' : '❌ false'));
        } catch (\Exception $e) {
            $this->error("hasRole() error: " . $e->getMessage());
        }

        $this->newLine();
    }

    private function checkGates()
    {
        $this->info('🚪 STEP 5: CHECKING GATES');
        $this->line('─────────────────────────');

        $superAdminRole = config('acl.super_admin_role', 'Super Admin');
        $user = User::whereHas('roles', function($query) use ($superAdminRole) {
            $query->where('name', $superAdminRole);
        })->first();

        if (!$user) {
            $this->error('❌ No Super Admin user to test gates');
            return;
        }

        // Test Gate::before for Super Admin bypass
        $testPermissions = ['documents.view', 'users.create', 'non.existent.permission'];
        
        foreach ($testPermissions as $permission) {
            try {
                $result = Gate::forUser($user)->allows($permission);
                $status = $result ? '✅' : '❌';
                $this->line("Gate::allows('{$permission}'): {$status}");
            } catch (\Exception $e) {
                $this->error("Gate error for {$permission}: " . $e->getMessage());
            }
        }

        $this->newLine();
    }
}
