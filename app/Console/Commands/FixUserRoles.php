<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\DB;

class FixUserRoles extends Command
{
    protected $signature = 'permission:fix-user-roles {--force : Force fix without confirmation}';
    protected $description = 'Fix user roles assignment after Spa<PERSON> migration';

    public function handle()
    {
        $this->info('🔧 FIXING USER ROLES ASSIGNMENT');
        $this->newLine();

        if (!$this->option('force') && !$this->confirm('This will assign Spatie roles to users based on their role_id. Continue?')) {
            $this->info('Operation cancelled.');
            return;
        }

        $this->fixUserRoles();
        $this->createSuperAdminIfNeeded();
        $this->verifyFix();

        $this->newLine();
        $this->info('✅ User roles fix completed!');
    }

    private function fixUserRoles()
    {
        $this->info('📋 Step 1: Fixing existing user roles...');

        // Get users with role_id
        $usersWithRoleId = User::whereNotNull('role_id')->get();
        
        if ($usersWithRoleId->isEmpty()) {
            $this->warn('⚠️ No users with role_id found');
            return;
        }

        $fixed = 0;
        $errors = 0;

        foreach ($usersWithRoleId as $user) {
            try {
                // Get the old role name from custom roles table
                $oldRole = DB::table('roles')->where('id', $user->role_id)->first();
                
                if (!$oldRole) {
                    $this->warn("⚠️ Role ID {$user->role_id} not found for user {$user->email}");
                    $errors++;
                    continue;
                }

                // Find corresponding Spatie role
                $spatieRole = Role::where('name', $oldRole->name)->first();
                
                if (!$spatieRole) {
                    $this->warn("⚠️ Spatie role '{$oldRole->name}' not found for user {$user->email}");
                    $errors++;
                    continue;
                }

                // Remove existing roles first
                $user->roles()->detach();
                
                // Assign the Spatie role
                $user->assignRole($spatieRole);
                
                $this->line("✅ Assigned role '{$spatieRole->name}' to {$user->email}");
                $fixed++;

            } catch (\Exception $e) {
                $this->error("❌ Error fixing user {$user->email}: " . $e->getMessage());
                $errors++;
            }
        }

        $this->info("Fixed: {$fixed} users, Errors: {$errors}");
    }

    private function createSuperAdminIfNeeded()
    {
        $this->info('📋 Step 2: Ensuring Super Admin exists...');

        $superAdminRole = Role::where('name', config('acl.super_admin_role', 'Super Admin'))->first();
        
        if (!$superAdminRole) {
            $this->error('❌ Super Admin role not found!');
            return;
        }

        // Check if any user has Super Admin role
        $existingSuperAdmin = User::whereHas('roles', function($query) use ($superAdminRole) {
            $query->where('name', $superAdminRole->name);
        })->first();

        if ($existingSuperAdmin) {
            $this->info("✅ Super Admin already exists: {$existingSuperAdmin->email}");
            return;
        }

        // Look for user with email containing 'admin'
        $adminUser = User::where('email', 'like', '%admin%')->first();
        
        if ($adminUser) {
            $adminUser->assignRole($superAdminRole);
            $this->info("✅ Assigned Super Admin role to: {$adminUser->email}");
        } else {
            $this->warn('⚠️ No admin user found to assign Super Admin role');
            $this->line('Available users:');
            User::all()->each(function($user) {
                $this->line("  - {$user->email}");
            });
        }
    }

    private function verifyFix()
    {
        $this->info('📋 Step 3: Verifying fix...');

        // Count users with Spatie roles
        $usersWithSpatieRoles = User::whereHas('roles')->count();
        $this->line("Users with Spatie roles: {$usersWithSpatieRoles}");

        // List all users and their roles
        $this->line('User roles summary:');
        User::with('roles')->get()->each(function($user) {
            $roles = $user->roles->pluck('name')->join(', ') ?: 'No roles';
            $this->line("  - {$user->email}: {$roles}");
        });

        // Test Super Admin
        $superAdminRole = config('acl.super_admin_role', 'Super Admin');
        $superAdmin = User::whereHas('roles', function($query) use ($superAdminRole) {
            $query->where('name', $superAdminRole);
        })->first();

        if ($superAdmin) {
            $this->info("✅ Super Admin found: {$superAdmin->email}");
            
            // Test Super Admin methods
            $isSuperAdmin = $superAdmin->isSuperAdmin();
            $hasPermission = $superAdmin->hasPermission('documents.view');
            
            $this->line("  - isSuperAdmin(): " . ($isSuperAdmin ? '✅ true' : '❌ false'));
            $this->line("  - hasPermission('documents.view'): " . ($hasPermission ? '✅ true' : '❌ false'));
        } else {
            $this->error('❌ No Super Admin found after fix!');
        }
    }
}
