<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class SyncPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'permission:sync {--force : Force sync without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync permissions from config/acl.php to database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting permission synchronization...');

        if (!$this->option('force') && !$this->confirm('This will sync permissions from config/acl.php. Continue?')) {
            $this->info('Operation cancelled.');
            return;
        }

        $config = config('acl');
        $guard = $config['default_guard'] ?? 'web';
        
        $this->syncPermissions($config, $guard);
        $this->syncRoles($config, $guard);
        
        $this->info('Permission synchronization completed successfully!');
    }

    /**
     * Sync permissions from config to database
     */
    private function syncPermissions(array $config, string $guard): void
    {
        $this->info('Syncing permissions...');
        
        $permissionsCreated = 0;
        $permissionsUpdated = 0;

        foreach ($config['modules'] as $module => $moduleConfig) {
            foreach ($moduleConfig['permissions'] as $action => $description) {
                $permissionName = "{$module}.{$action}";
                
                $permission = Permission::firstOrCreate([
                    'name' => $permissionName,
                    'guard_name' => $guard,
                ]);

                if ($permission->wasRecentlyCreated) {
                    $permissionsCreated++;
                    $this->line("  ✓ Created permission: {$permissionName}");
                } else {
                    $permissionsUpdated++;
                }
            }
        }

        $this->info("Permissions synced: {$permissionsCreated} created, {$permissionsUpdated} updated");
    }

    /**
     * Sync roles from config to database
     */
    private function syncRoles(array $config, string $guard): void
    {
        $this->info('Syncing roles...');
        
        $rolesCreated = 0;
        $rolesUpdated = 0;

        foreach ($config['roles'] as $roleName => $roleConfig) {
            $role = Role::firstOrCreate([
                'name' => $roleName,
                'guard_name' => $guard,
            ]);

            if ($role->wasRecentlyCreated) {
                $rolesCreated++;
                $this->line("  ✓ Created role: {$roleName}");
            } else {
                $rolesUpdated++;
            }

            // Sync permissions for role (except Super Admin)
            if ($roleName !== $config['super_admin_role']) {
                $this->syncRolePermissions($role, $roleConfig['permissions'], $config);
            }
        }

        $this->info("Roles synced: {$rolesCreated} created, {$rolesUpdated} updated");
    }

    /**
     * Sync permissions for a specific role
     */
    private function syncRolePermissions(Role $role, $permissions, array $config): void
    {
        if ($permissions === '*') {
            // Give all permissions
            $allPermissions = Permission::where('guard_name', $config['default_guard'])->get();
            $role->syncPermissions($allPermissions);
            return;
        }

        $permissionNames = [];
        
        foreach ($permissions as $permission) {
            if (str_ends_with($permission, '.*')) {
                // Module wildcard (e.g., 'users.*')
                $module = str_replace('.*', '', $permission);
                if (isset($config['modules'][$module])) {
                    foreach ($config['modules'][$module]['permissions'] as $action => $description) {
                        $permissionNames[] = "{$module}.{$action}";
                    }
                }
            } elseif (str_ends_with($permission, '.view')) {
                // View wildcard (e.g., '*.view')
                foreach ($config['modules'] as $module => $moduleConfig) {
                    if (isset($moduleConfig['permissions']['view'])) {
                        $permissionNames[] = "{$module}.view";
                    }
                }
            } else {
                // Specific permission
                $permissionNames[] = $permission;
            }
        }

        $permissionModels = Permission::whereIn('name', $permissionNames)
            ->where('guard_name', $config['default_guard'])
            ->get();
            
        $role->syncPermissions($permissionModels);
        
        $this->line("  ✓ Synced {$permissionModels->count()} permissions for role: {$role->name}");
    }
}
