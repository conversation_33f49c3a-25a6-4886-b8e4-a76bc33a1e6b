<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;

class TestRoutes extends Command
{
    protected $signature = 'permission:test-routes {--user-email=<EMAIL>}';
    protected $description = 'Test routes with permission system';

    public function handle()
    {
        $this->info('🧪 TESTING ROUTES WITH PERMISSION SYSTEM');
        $this->newLine();

        $email = $this->option('user-email');
        $user = User::where('email', $email)->first();

        if (!$user) {
            $this->error("User {$email} not found!");
            return;
        }

        $this->info("Testing with user: {$user->email}");
        $this->line("Is Super Admin: " . ($user->isSuperAdmin() ? 'YES' : 'NO'));
        $this->newLine();

        // Test key routes
        $routesToTest = [
            '/documents' => 'documents.view',
            '/documents/wizard' => 'documents.create', 
            '/litigants' => 'litigants.view',
            '/admin/users' => 'users.view',
        ];

        foreach ($routesToTest as $route => $permission) {
            $this->testRoute($user, $route, $permission);
        }

        $this->newLine();
        $this->info('✅ Route testing completed!');
    }

    private function testRoute($user, $route, $permission)
    {
        $this->line("Testing route: {$route}");
        
        // Test permission directly
        $hasPermission = $user->hasPermission($permission);
        $this->line("  Permission '{$permission}': " . ($hasPermission ? '✅ ALLOWED' : '❌ DENIED'));

        // Check if route exists
        $routeExists = Route::has(ltrim($route, '/'));
        if (!$routeExists) {
            // Try to find route by URL
            $routes = Route::getRoutes();
            $found = false;
            foreach ($routes as $r) {
                if ($r->uri() === ltrim($route, '/')) {
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                $this->line("  Route status: ⚠️ NOT FOUND");
                return;
            }
        }

        $this->line("  Route status: ✅ EXISTS");
        
        // Simulate middleware check
        try {
            // This simulates what CheckPermission middleware would do
            if (!$user->hasPermission($permission)) {
                $this->line("  Middleware: ❌ WOULD BLOCK (403)");
            } else {
                $this->line("  Middleware: ✅ WOULD ALLOW");
            }
        } catch (\Exception $e) {
            $this->line("  Middleware: ❌ ERROR - " . $e->getMessage());
        }

        $this->newLine();
    }
}
