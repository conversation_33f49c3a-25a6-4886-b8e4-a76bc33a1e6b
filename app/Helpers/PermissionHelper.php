<?php

if (!function_exists('userCan')) {
    /**
     * Check if current user has permission
     */
    function userCan(string $permission): bool
    {
        if (!auth()->check()) {
            return false;
        }

        return auth()->user()->hasPermission($permission);
    }
}

if (!function_exists('userCanAny')) {
    /**
     * Check if current user has any of the given permissions
     */
    function userCanAny(array $permissions): bool
    {
        if (!auth()->check()) {
            return false;
        }

        $user = auth()->user();
        
        if ($user->isSuperAdmin()) {
            return true;
        }

        return $user->hasAnyPermission($permissions);
    }
}

if (!function_exists('userCanAll')) {
    /**
     * Check if current user has all of the given permissions
     */
    function userCanAll(array $permissions): bool
    {
        if (!auth()->check()) {
            return false;
        }

        $user = auth()->user();
        
        if ($user->isSuperAdmin()) {
            return true;
        }

        return $user->hasAllPermissions($permissions);
    }
}

if (!function_exists('userHasRole')) {
    /**
     * Check if current user has role
     */
    function userHasRole(string $role): bool
    {
        if (!auth()->check()) {
            return false;
        }

        return auth()->user()->hasRole($role);
    }
}

if (!function_exists('userHasAnyRole')) {
    /**
     * Check if current user has any of the given roles
     */
    function userHasAnyRole(array $roles): bool
    {
        if (!auth()->check()) {
            return false;
        }

        return auth()->user()->hasAnyRole($roles);
    }
}

if (!function_exists('isSuperAdmin')) {
    /**
     * Check if current user is Super Admin
     */
    function isSuperAdmin(): bool
    {
        if (!auth()->check()) {
            return false;
        }

        return auth()->user()->isSuperAdmin();
    }
}

if (!function_exists('canAccessMenu')) {
    /**
     * Check if current user can access menu item
     */
    function canAccessMenu(string $menuSlug): bool
    {
        if (!auth()->check()) {
            return false;
        }

        $permissionService = app(\App\Services\PermissionService::class);
        return $permissionService->canAccessMenuItem(auth()->user(), $menuSlug);
    }
}

if (!function_exists('getUserPermissions')) {
    /**
     * Get current user's permissions
     */
    function getUserPermissions(): array
    {
        if (!auth()->check()) {
            return [];
        }

        $user = auth()->user();
        
        if ($user->isSuperAdmin()) {
            // Return all permissions for Super Admin
            return \Spatie\Permission\Models\Permission::pluck('name')->toArray();
        }

        return $user->getAllPermissions()->pluck('name')->toArray();
    }
}

if (!function_exists('getUserRoles')) {
    /**
     * Get current user's roles
     */
    function getUserRoles(): array
    {
        if (!auth()->check()) {
            return [];
        }

        return auth()->user()->getRoleNames()->toArray();
    }
}

if (!function_exists('formatPermissionName')) {
    /**
     * Format permission name for display
     */
    function formatPermissionName(string $permission): string
    {
        $parts = explode('.', $permission);
        
        if (count($parts) !== 2) {
            return $permission;
        }

        $module = $parts[0];
        $action = $parts[1];

        $moduleNames = [
            'users' => 'Người dùng',
            'documents' => 'Hồ sơ',
            'litigants' => 'Đương sự',
            'roles' => 'Vai trò',
            'asset-templates' => 'Template tài sản',
            'asset-fields' => 'Field động',
            'contract-types' => 'Loại hợp đồng',
            'notaries' => 'Công chứng',
            'assets' => 'Tài sản',
            'template-assets' => 'Template tài sản',
            'parties' => 'Bên liên quan',
            'template-parties' => 'Template bên liên quan',
        ];

        $actionNames = [
            'view' => 'Xem',
            'create' => 'Tạo',
            'edit' => 'Sửa',
            'delete' => 'Xóa',
            'export' => 'Xuất',
            'select' => 'Chọn',
            'manage-selection' => 'Quản lý lựa chọn',
        ];

        $moduleName = $moduleNames[$module] ?? ucfirst($module);
        $actionName = $actionNames[$action] ?? ucfirst($action);

        return "{$actionName} {$moduleName}";
    }
}

if (!function_exists('getPermissionsByModule')) {
    /**
     * Get permissions grouped by module for current user
     */
    function getPermissionsByModule(): array
    {
        if (!auth()->check()) {
            return [];
        }

        $permissionService = app(\App\Services\PermissionService::class);
        return $permissionService->getUserPermissionsByModule(auth()->user());
    }
}

if (!function_exists('canManageRoles')) {
    /**
     * Check if current user can manage roles
     */
    function canManageRoles(): bool
    {
        return userCan('roles.create') || userCan('roles.edit') || userCan('roles.delete');
    }
}

if (!function_exists('canManageUsers')) {
    /**
     * Check if current user can manage users
     */
    function canManageUsers(): bool
    {
        return userCan('users.create') || userCan('users.edit') || userCan('users.delete');
    }
}

if (!function_exists('canManageDocuments')) {
    /**
     * Check if current user can manage documents
     */
    function canManageDocuments(): bool
    {
        return userCan('documents.create') || userCan('documents.edit') || userCan('documents.delete');
    }
}
