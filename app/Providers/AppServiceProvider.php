<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Vite;
use Illuminate\Support\Facades\Blade;

class AppServiceProvider extends ServiceProvider
{
  /**
   * Register any application services.
   */
  public function register(): void
  {
    //
  }

  /**
   * Bootstrap any application services.
   */
  public function boot(): void
  {
    Vite::useStyleTagAttributes(function (?string $src, string $url, ?array $chunk, ?array $manifest) {
      if ($src !== null) {
        return [
          'class' => preg_match("/(resources\/assets\/vendor\/scss\/(rtl\/)?core)-?.*/i", $src) ? 'template-customizer-core-css' :
                    (preg_match("/(resources\/assets\/vendor\/scss\/(rtl\/)?theme)-?.*/i", $src) ? 'template-customizer-theme-css' : '')
        ];
      }
      return [];
    });

    // Register custom Blade directives for permissions
    $this->registerBladeDirectives();
  }

  /**
   * Register custom Blade directives
   */
  private function registerBladeDirectives(): void
  {
    // @userCan directive
    Blade::directive('userCan', function ($permission) {
      return "<?php if(userCan({$permission})): ?>";
    });

    Blade::directive('enduserCan', function () {
      return "<?php endif; ?>";
    });

    // @userCanAny directive
    Blade::directive('userCanAny', function ($permissions) {
      return "<?php if(userCanAny({$permissions})): ?>";
    });

    Blade::directive('enduserCanAny', function () {
      return "<?php endif; ?>";
    });

    // @isSuperAdmin directive
    Blade::directive('isSuperAdmin', function () {
      return "<?php if(isSuperAdmin()): ?>";
    });

    Blade::directive('endisSuperAdmin', function () {
      return "<?php endif; ?>";
    });

    // @canAccessMenu directive
    Blade::directive('canAccessMenu', function ($menuSlug) {
      return "<?php if(canAccessMenu({$menuSlug})): ?>";
    });

    Blade::directive('endcanAccessMenu', function () {
      return "<?php endif; ?>";
    });
  }
}