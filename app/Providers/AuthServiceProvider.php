<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        //
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        // Super Admin bypass - must be first
        Gate::before(function ($user, $ability) {
            // Super Admin bypasses all permission checks
            if ($user->isSuperAdmin()) {
                return true;
            }

            return null; // Let Spatie handle the rest
        });

        // Register Spatie Permission gates
        $this->registerPermissionGates();
    }

    /**
     * Register gates for all permissions defined in ACL config
     */
    private function registerPermissionGates(): void
    {
        $aclConfig = config('acl');

        if (!$aclConfig || !isset($aclConfig['modules'])) {
            return;
        }

        foreach ($aclConfig['modules'] as $module => $moduleConfig) {
            foreach ($moduleConfig['permissions'] as $action => $description) {
                $permission = "{$module}.{$action}";

                Gate::define($permission, function ($user) use ($permission) {
                    return $user->can($permission);
                });
            }
        }
        
        Gate::define('asset-fields.delete', function ($user) {
            return $user->hasPermission('asset-fields.delete');
        });
    }
}
