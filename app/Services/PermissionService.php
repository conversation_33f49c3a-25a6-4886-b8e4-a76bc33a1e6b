<?php

namespace App\Services;

use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use App\Models\User;
use Illuminate\Support\Collection;

class PermissionService
{
    /**
     * Get all permissions grouped by module
     */
    public function getPermissionsByModule(): array
    {
        $aclConfig = config('acl');
        $permissions = [];

        if (!$aclConfig || !isset($aclConfig['modules'])) {
            return $permissions;
        }

        foreach ($aclConfig['modules'] as $module => $moduleConfig) {
            $permissions[$module] = [
                'name' => $moduleConfig['name'],
                'permissions' => []
            ];

            foreach ($moduleConfig['permissions'] as $action => $description) {
                $permissionName = "{$module}.{$action}";
                $permission = Permission::where('name', $permissionName)->first();
                
                if ($permission) {
                    $permissions[$module]['permissions'][] = [
                        'id' => $permission->id,
                        'name' => $permissionName,
                        'action' => $action,
                        'description' => $description,
                    ];
                }
            }
        }

        return $permissions;
    }

    /**
     * Get all roles with their permissions
     */
    public function getRolesWithPermissions(): Collection
    {
        return Role::with('permissions')->get();
    }

    /**
     * Check if user has permission (with Super Admin bypass)
     */
    public function userHasPermission(User $user, string $permission): bool
    {
        return $user->hasPermission($permission);
    }

    /**
     * Check if user has any of the given permissions
     */
    public function userHasAnyPermission(User $user, array $permissions): bool
    {
        if ($user->isSuperAdmin()) {
            return true;
        }

        return $user->hasAnyPermission($permissions);
    }

    /**
     * Check if user has all of the given permissions
     */
    public function userHasAllPermissions(User $user, array $permissions): bool
    {
        if ($user->isSuperAdmin()) {
            return true;
        }

        return $user->hasAllPermissions($permissions);
    }

    /**
     * Get user's permissions grouped by module
     */
    public function getUserPermissionsByModule(User $user): array
    {
        if ($user->isSuperAdmin()) {
            return $this->getPermissionsByModule();
        }

        $userPermissions = $user->getAllPermissions()->pluck('name')->toArray();
        $allPermissions = $this->getPermissionsByModule();

        foreach ($allPermissions as $module => &$moduleData) {
            $moduleData['permissions'] = array_filter(
                $moduleData['permissions'],
                function ($permission) use ($userPermissions) {
                    return in_array($permission['name'], $userPermissions);
                }
            );
        }

        return $allPermissions;
    }

    /**
     * Assign role to user
     */
    public function assignRoleToUser(User $user, string $roleName): bool
    {
        try {
            $user->assignRole($roleName);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Remove role from user
     */
    public function removeRoleFromUser(User $user, string $roleName): bool
    {
        try {
            $user->removeRole($roleName);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Sync permissions for role
     */
    public function syncRolePermissions(Role $role, array $permissionIds): bool
    {
        try {
            $permissions = Permission::whereIn('id', $permissionIds)->get();
            $role->syncPermissions($permissions);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Get permissions for menu visibility
     */
    public function getMenuPermissions(): array
    {
        return [
            'documents' => [
                'view' => 'documents.view',
                'create' => 'documents.create',
            ],
            'litigants' => [
                'view' => 'litigants.view',
                'create' => 'litigants.create',
            ],
            'admin' => [
                'users' => 'users.view',
                'roles' => 'roles.view',
            ],
            'asset-templates' => [
                'view' => 'asset-templates.view',
                'create' => 'asset-templates.create',
            ],
            'asset-fields' => [
                'view' => 'asset-fields.view',
                'create' => 'asset-fields.create',
            ],
            'contract-types' => [
                'view' => 'contract-types.view',
                'create' => 'contract-types.create',
            ],
        ];
    }

    /**
     * Check if user can access menu item
     */
    public function canAccessMenuItem(User $user, string $menuSlug): bool
    {
        $menuPermissions = $this->getMenuPermissions();

        // Map menu slugs to permissions
        $permissionMap = [
            'documents-list' => 'documents.view',
            'documents-create' => 'documents.create',
            'litigants' => 'litigants.view',
            'admin-users' => 'users.view',
            'admin-users-create' => 'users.create',
            'roles' => 'roles.view',
            'asset-templates' => 'asset-templates.view',
            'asset-fields' => 'asset-fields.view',
            'contract-types' => 'contract-types.view',
        ];

        if (!isset($permissionMap[$menuSlug])) {
            return true; // Allow access if no specific permission required
        }

        return $user->hasPermission($permissionMap[$menuSlug]);
    }

    /**
     * Get available permissions for role assignment
     */
    public function getAvailablePermissions(): Collection
    {
        return Permission::orderBy('name')->get();
    }

    /**
     * Create new permission
     */
    public function createPermission(string $name, string $guardName = 'web'): ?Permission
    {
        try {
            return Permission::create([
                'name' => $name,
                'guard_name' => $guardName,
            ]);
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Create new role
     */
    public function createRole(string $name, string $guardName = 'web'): ?Role
    {
        try {
            return Role::create([
                'name' => $name,
                'guard_name' => $guardName,
            ]);
        } catch (\Exception $e) {
            return null;
        }
    }
}
