<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use App\Models\Role as CustomRole;
use App\Models\Permission as CustomPermission;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Skip migration if running in testing environment
        if (app()->environment('testing')) {
            return;
        }

        $this->info('Starting migration to Spatie Permission system...');

        // Step 1: Backup existing data
        $this->backupExistingData();

        // Step 2: Migrate permissions from custom table to Spatie
        $this->migratePermissions();

        // Step 3: Migrate roles from custom table to Spatie
        $this->migrateRoles();

        // Step 4: Migrate role-permission relationships
        $this->migrateRolePermissions();

        // Step 5: Migrate user-role relationships
        $this->migrateUserRoles();

        // Step 6: Verify data integrity
        $this->verifyDataIntegrity();

        $this->info('Migration to Spatie Permission system completed successfully!');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $this->info('Rolling back Spatie Permission migration...');

        // Clear Spatie tables
        DB::table('model_has_roles')->truncate();
        DB::table('model_has_permissions')->truncate();
        DB::table('role_has_permissions')->truncate();

        // Restore users role_id from backup
        $backupUsers = DB::table('migration_backup_users')->get();
        foreach ($backupUsers as $backupUser) {
            DB::table('users')
                ->where('id', $backupUser->id)
                ->update(['role_id' => $backupUser->role_id]);
        }

        // Drop backup tables
        Schema::dropIfExists('migration_backup_users');
        Schema::dropIfExists('migration_backup_roles');
        Schema::dropIfExists('migration_backup_permissions');
        Schema::dropIfExists('migration_backup_role_has_permissions');

        $this->info('Rollback completed successfully!');
    }

    private function info(string $message): void
    {
        echo "[INFO] {$message}\n";
    }

    private function backupExistingData(): void
    {
        $this->info('Creating backup of existing data...');

        // Backup users table
        DB::statement('CREATE TABLE migration_backup_users AS SELECT * FROM users');

        // Backup roles table
        DB::statement('CREATE TABLE migration_backup_roles AS SELECT * FROM roles');

        // Backup permissions table
        DB::statement('CREATE TABLE migration_backup_permissions AS SELECT * FROM permissions');

        // Backup role_has_permissions table
        DB::statement('CREATE TABLE migration_backup_role_has_permissions AS SELECT * FROM role_has_permissions');

        $this->info('Backup completed successfully!');
    }

    private function migratePermissions(): void
    {
        $this->info('Migrating permissions...');

        $customPermissions = DB::table('permissions')->get();
        $migratedCount = 0;

        foreach ($customPermissions as $customPermission) {
            // Check if permission already exists in Spatie table
            $existingPermission = Permission::where('name', $customPermission->name)
                ->where('guard_name', $customPermission->guard_name)
                ->first();

            if (!$existingPermission) {
                Permission::create([
                    'name' => $customPermission->name,
                    'guard_name' => $customPermission->guard_name,
                    'created_at' => $customPermission->created_at,
                    'updated_at' => $customPermission->updated_at,
                ]);
                $migratedCount++;
            }
        }

        $this->info("Migrated {$migratedCount} permissions to Spatie system");
    }

    private function migrateRoles(): void
    {
        $this->info('Migrating roles...');

        $customRoles = DB::table('roles')->get();
        $migratedCount = 0;

        foreach ($customRoles as $customRole) {
            // Check if role already exists in Spatie table
            $existingRole = Role::where('name', $customRole->name)
                ->where('guard_name', $customRole->guard_name)
                ->first();

            if (!$existingRole) {
                Role::create([
                    'name' => $customRole->name,
                    'guard_name' => $customRole->guard_name,
                    'created_at' => $customRole->created_at,
                    'updated_at' => $customRole->updated_at,
                ]);
                $migratedCount++;
            }
        }

        $this->info("Migrated {$migratedCount} roles to Spatie system");
    }

    private function migrateRolePermissions(): void
    {
        $this->info('Migrating role-permission relationships...');

        $rolePermissions = DB::table('role_has_permissions')->get();
        $migratedCount = 0;

        foreach ($rolePermissions as $rolePermission) {
            // Get the corresponding Spatie role and permission
            $customRole = DB::table('roles')->where('id', $rolePermission->role_id)->first();
            $customPermission = DB::table('permissions')->where('id', $rolePermission->permission_id)->first();

            if ($customRole && $customPermission) {
                $spatieRole = Role::where('name', $customRole->name)
                    ->where('guard_name', $customRole->guard_name)
                    ->first();

                $spatiePermission = Permission::where('name', $customPermission->name)
                    ->where('guard_name', $customPermission->guard_name)
                    ->first();

                if ($spatieRole && $spatiePermission) {
                    // Check if relationship already exists
                    $exists = DB::table('role_has_permissions')
                        ->where('role_id', $spatieRole->id)
                        ->where('permission_id', $spatiePermission->id)
                        ->exists();

                    if (!$exists) {
                        $spatieRole->givePermissionTo($spatiePermission);
                        $migratedCount++;
                    }
                }
            }
        }

        $this->info("Migrated {$migratedCount} role-permission relationships");
    }

    private function migrateUserRoles(): void
    {
        $this->info('Migrating user-role relationships...');

        $users = User::whereNotNull('role_id')->get();
        $migratedCount = 0;

        foreach ($users as $user) {
            $customRole = DB::table('roles')->where('id', $user->role_id)->first();

            if ($customRole) {
                $spatieRole = Role::where('name', $customRole->name)
                    ->where('guard_name', $customRole->guard_name)
                    ->first();

                if ($spatieRole) {
                    // Remove existing roles first
                    $user->roles()->detach();

                    // Assign the role using Spatie
                    $user->assignRole($spatieRole);
                    $migratedCount++;
                }
            }
        }

        $this->info("Migrated {$migratedCount} user-role relationships");
    }

    private function verifyDataIntegrity(): void
    {
        $this->info('Verifying data integrity...');

        $customRoleCount = DB::table('roles')->count();
        $spatieRoleCount = Role::count();

        $customPermissionCount = DB::table('permissions')->count();
        $spatiePermissionCount = Permission::count();

        $usersWithRoles = User::whereNotNull('role_id')->count();
        $usersWithSpatieRoles = User::whereHas('roles')->count();

        $this->info("Verification Results:");
        $this->info("- Custom Roles: {$customRoleCount} | Spatie Roles: {$spatieRoleCount}");
        $this->info("- Custom Permissions: {$customPermissionCount} | Spatie Permissions: {$spatiePermissionCount}");
        $this->info("- Users with role_id: {$usersWithRoles} | Users with Spatie roles: {$usersWithSpatieRoles}");

        if ($spatieRoleCount >= $customRoleCount &&
            $spatiePermissionCount >= $customPermissionCount &&
            $usersWithSpatieRoles >= $usersWithRoles) {
            $this->info('✓ Data integrity verification passed!');
        } else {
            $this->info('⚠ Data integrity verification failed! Please check the migration.');
        }
    }
};
