<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class SpatiePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Starting Spatie Permission seeder...');

        // Sync permissions from config
        $this->call(\App\Console\Commands\SyncPermissions::class);

        // Create default Super Admin user if not exists
        $this->createDefaultSuperAdmin();

        $this->command->info('Spatie Permission seeder completed successfully!');
    }

    /**
     * Create default Super Admin user
     */
    private function createDefaultSuperAdmin(): void
    {
        $superAdminRole = Role::where('name', config('acl.super_admin_role', 'Super Admin'))->first();

        if (!$superAdminRole) {
            $this->command->error('Super Admin role not found! Please run permission:sync first.');
            return;
        }

        // Check if Super Admin user already exists
        $existingSuperAdmin = User::whereHas('roles', function ($query) use ($superAdminRole) {
            $query->where('name', $superAdminRole->name);
        })->first();

        if ($existingSuperAdmin) {
            $this->command->info("Super Admin user already exists: {$existingSuperAdmin->email}");
            return;
        }

        // Create new Super Admin user
        $superAdmin = User::create([
            'name' => 'Super Administrator',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'status' => 'active',
            'email_verified_at' => now(),
        ]);

        // Assign Super Admin role
        $superAdmin->assignRole($superAdminRole);

        $this->command->info("Created Super Admin user: {$superAdmin->email}");
        $this->command->warn("Default password: password (Please change this immediately!)");
    }
}
