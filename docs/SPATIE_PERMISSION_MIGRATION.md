# Hướng dẫn sử dụng hệ thống Spatie Laravel Permission

## Tổng quan

Hệ thống đã được chuyển đổi từ custom role/permission sang sử dụng Spatie Laravel Permission package. Điều này mang lại nhiều lợi ích:

- <PERSON><PERSON><PERSON> hóa theo best practices củ<PERSON>
- Hiệu suất tốt hơn với caching
- T<PERSON>h năng phong phú hơn (multiple roles, direct permissions, etc.)
- <PERSON><PERSON> bảo trì và mở rộng

## Cấu trúc mới

### 1. Config file: `config/acl.php`

Tất cả permissions được định nghĩa trong file này theo cấu trúc modules:

```php
'modules' => [
    'users' => [
        'name' => 'Quản lý người dùng',
        'permissions' => [
            'view' => 'Xem danh sách người dùng',
            'create' => 'Tạo người dùng mới',
            'edit' => 'Chỉnh sửa người dùng',
            'delete' => '<PERSON>óa người dùng',
        ],
    ],
    // ... other modules
]
```

### 2. Artisan Command: `php artisan permission:sync`

Đồng bộ permissions từ config vào database:

```bash
# Sync với confirmation
php artisan permission:sync

# Sync without confirmation
php artisan permission:sync --force
```

### 3. Super Admin Logic

Super Admin tự động có quyền truy cập tất cả chức năng mà không cần assign từng permission cụ thể.

## Cách sử dụng

### 1. Trong Controllers

```php
// Check permission (Super Admin tự động pass)
if (!Auth::user()->hasPermission('users.view')) {
    abort(403, 'Không có quyền truy cập');
}

// Hoặc sử dụng middleware
Route::get('/users', [UserController::class, 'index'])
    ->middleware('permission:users.view');
```

### 2. Trong Blade Templates

```blade
{{-- Sử dụng helper functions --}}
@if(userCan('users.create'))
    <a href="{{ route('users.create') }}">Tạo người dùng</a>
@endif

{{-- Sử dụng custom directives --}}
@userCan('users.edit')
    <button>Chỉnh sửa</button>
@enduserCan

{{-- Check multiple permissions --}}
@userCanAny(['users.create', 'users.edit'])
    <div>Có quyền tạo hoặc sửa</div>
@enduserCanAny

{{-- Check Super Admin --}}
@isSuperAdmin
    <div>Chỉ Super Admin mới thấy</div>
@endisSuperAdmin

{{-- Check menu access --}}
@canAccessMenu('admin-users')
    <li><a href="/admin/users">Quản lý người dùng</a></li>
@endcanAccessMenu
```

### 3. Sử dụng PermissionService

```php
use App\Services\PermissionService;

$permissionService = app(PermissionService::class);

// Get permissions by module
$permissions = $permissionService->getPermissionsByModule();

// Check user permissions
$canEdit = $permissionService->userHasPermission($user, 'users.edit');

// Assign role to user
$permissionService->assignRoleToUser($user, 'Admin');

// Sync role permissions
$permissionService->syncRolePermissions($role, [1, 2, 3]);
```

## Helper Functions

### Permission Helpers

- `userCan($permission)` - Check if current user has permission
- `userCanAny($permissions)` - Check if user has any of the permissions
- `userCanAll($permissions)` - Check if user has all permissions
- `userHasRole($role)` - Check if user has role
- `userHasAnyRole($roles)` - Check if user has any of the roles
- `isSuperAdmin()` - Check if current user is Super Admin

### Utility Helpers

- `canAccessMenu($menuSlug)` - Check menu access
- `getUserPermissions()` - Get current user's permissions
- `getUserRoles()` - Get current user's roles
- `formatPermissionName($permission)` - Format permission for display
- `getPermissionsByModule()` - Get permissions grouped by module

## Quản lý Permissions

### 1. Thêm Permission mới

1. Cập nhật `config/acl.php`:
```php
'new-module' => [
    'name' => 'Module mới',
    'permissions' => [
        'view' => 'Xem module mới',
        'create' => 'Tạo trong module mới',
    ],
],
```

2. Chạy sync command:
```bash
php artisan permission:sync --force
```

### 2. Tạo Role mới

```php
use Spatie\Permission\Models\Role;

$role = Role::create(['name' => 'New Role']);

// Assign permissions
$role->givePermissionTo(['users.view', 'documents.view']);
```

### 3. Assign Role cho User

```php
$user = User::find(1);
$user->assignRole('Admin');

// Hoặc assign multiple roles
$user->assignRole(['Admin', 'Editor']);
```

## Migration và Rollback

### Backup Data

Migration tự động tạo backup tables:
- `migration_backup_users`
- `migration_backup_roles`
- `migration_backup_permissions`
- `migration_backup_role_has_permissions`

### Rollback

Nếu cần rollback:

```bash
php artisan migrate:rollback --step=1
```

## Testing

### Test Permission System

```php
// Test Super Admin
$superAdmin = User::factory()->create();
$superAdmin->assignRole('Super Admin');
$this->assertTrue($superAdmin->isSuperAdmin());
$this->assertTrue($superAdmin->hasPermission('any.permission'));

// Test Regular User
$user = User::factory()->create();
$user->assignRole('Admin');
$this->assertFalse($user->isSuperAdmin());
$this->assertTrue($user->hasPermission('users.view'));
```

## Troubleshooting

### 1. Permission không hoạt động

- Kiểm tra cache: `php artisan permission:cache-reset`
- Kiểm tra config: `php artisan config:clear`
- Chạy lại sync: `php artisan permission:sync --force`

### 2. User không có quyền sau migration

- Kiểm tra user có role: `$user->roles`
- Kiểm tra role có permissions: `$role->permissions`
- Verify migration: Check backup tables

### 3. Super Admin không bypass được

- Kiểm tra role name trong config: `config('acl.super_admin_role')`
- Kiểm tra user có role Super Admin: `$user->hasRole('Super Admin')`

## Best Practices

1. **Luôn sử dụng config/acl.php** để định nghĩa permissions
2. **Không hardcode permission names** trong code
3. **Sử dụng helper functions** thay vì gọi trực tiếp Spatie methods
4. **Test permissions** trong unit tests
5. **Backup database** trước khi thay đổi permissions
6. **Sử dụng middleware** để protect routes
7. **Cache permissions** trong production

## Performance Tips

1. Sử dụng `with('roles.permissions')` khi load users
2. Cache permission checks trong views
3. Sử dụng `hasAnyPermission()` thay vì multiple `hasPermission()`
4. Enable permission caching trong production
