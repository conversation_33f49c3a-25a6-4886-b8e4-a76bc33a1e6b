<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class ControllerPermissionTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create permissions
        Permission::create(['name' => 'documents.view', 'guard_name' => 'web']);
        Permission::create(['name' => 'documents.create', 'guard_name' => 'web']);
        Permission::create(['name' => 'litigants.view', 'guard_name' => 'web']);
        Permission::create(['name' => 'litigants.create', 'guard_name' => 'web']);
        
        // Create roles
        Role::create(['name' => 'Super Admin', 'guard_name' => 'web']);
        $adminRole = Role::create(['name' => 'Admin', 'guard_name' => 'web']);
        $viewerRole = Role::create(['name' => 'Viewer', 'guard_name' => 'web']);
        
        // Assign permissions to roles
        $adminRole->givePermissionTo(['documents.view', 'documents.create', 'litigants.view']);
        $viewerRole->givePermissionTo(['documents.view', 'litigants.view']);
    }

    public function test_super_admin_can_access_all_routes(): void
    {
        $user = User::factory()->create();
        $user->assignRole('Super Admin');

        // Test documents routes
        $response = $this->actingAs($user)->get('/documents');
        $this->assertEquals(200, $response->getStatusCode());

        $response = $this->actingAs($user)->get('/documents/wizard');
        $this->assertEquals(200, $response->getStatusCode());

        // Test litigants routes
        $response = $this->actingAs($user)->get('/litigants');
        $this->assertEquals(200, $response->getStatusCode());
    }

    public function test_admin_can_access_permitted_routes(): void
    {
        $user = User::factory()->create();
        $user->assignRole('Admin');

        // Should have access to documents
        $response = $this->actingAs($user)->get('/documents');
        $this->assertEquals(200, $response->getStatusCode());

        $response = $this->actingAs($user)->get('/documents/wizard');
        $this->assertEquals(200, $response->getStatusCode());

        // Should have access to litigants view
        $response = $this->actingAs($user)->get('/litigants');
        $this->assertEquals(200, $response->getStatusCode());
    }

    public function test_viewer_cannot_access_create_routes(): void
    {
        $user = User::factory()->create();
        $user->assignRole('Viewer');

        // Should have access to view routes
        $response = $this->actingAs($user)->get('/documents');
        $this->assertEquals(200, $response->getStatusCode());

        $response = $this->actingAs($user)->get('/litigants');
        $this->assertEquals(200, $response->getStatusCode());

        // Should NOT have access to create routes
        $response = $this->actingAs($user)->get('/documents/wizard');
        $this->assertEquals(403, $response->getStatusCode());
    }

    public function test_user_without_role_cannot_access_protected_routes(): void
    {
        $user = User::factory()->create();
        // No role assigned

        $response = $this->actingAs($user)->get('/documents');
        $this->assertEquals(403, $response->getStatusCode());

        $response = $this->actingAs($user)->get('/documents/wizard');
        $this->assertEquals(403, $response->getStatusCode());

        $response = $this->actingAs($user)->get('/litigants');
        $this->assertEquals(403, $response->getStatusCode());
    }

    public function test_unauthenticated_user_redirected_to_login(): void
    {
        $response = $this->get('/documents');
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertStringContains('login', $response->headers->get('Location'));

        $response = $this->get('/litigants');
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertStringContains('login', $response->headers->get('Location'));
    }

    public function test_permission_helper_functions_work_in_controllers(): void
    {
        $user = User::factory()->create();
        $user->assignRole('Super Admin');
        
        $this->actingAs($user);

        // Test helper functions
        $this->assertTrue(userCan('documents.view'));
        $this->assertTrue(userCan('documents.create'));
        $this->assertTrue(userCan('any.permission'));
        $this->assertTrue(isSuperAdmin());
        $this->assertTrue(userHasRole('Super Admin'));
    }

    public function test_regular_user_helper_functions(): void
    {
        $user = User::factory()->create();
        $user->assignRole('Admin');
        
        $this->actingAs($user);

        // Test helper functions for regular user
        $this->assertTrue(userCan('documents.view'));
        $this->assertTrue(userCan('documents.create'));
        $this->assertFalse(userCan('non.existent.permission'));
        $this->assertFalse(isSuperAdmin());
        $this->assertTrue(userHasRole('Admin'));
        $this->assertFalse(userHasRole('Super Admin'));
    }
}
