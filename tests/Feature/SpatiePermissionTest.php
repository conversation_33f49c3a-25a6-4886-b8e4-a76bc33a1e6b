<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class SpatiePermissionTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create permissions
        Permission::create(['name' => 'users.view', 'guard_name' => 'web']);
        Permission::create(['name' => 'users.create', 'guard_name' => 'web']);
        Permission::create(['name' => 'documents.view', 'guard_name' => 'web']);

        // Create roles
        Role::create(['name' => 'Super Admin', 'guard_name' => 'web']);
        Role::create(['name' => 'Admin', 'guard_name' => 'web']);
    }

    public function test_super_admin_bypasses_all_permissions(): void
    {
        $user = User::factory()->create();
        $user->assignRole('Super Admin');

        $this->assertTrue($user->isSuperAdmin());
        $this->assertTrue($user->hasPermission('users.view'));
        $this->assertTrue($user->hasPermission('users.create'));
        $this->assertTrue($user->hasPermission('any.permission.that.does.not.exist'));
    }

    public function test_regular_user_respects_permissions(): void
    {
        $user = User::factory()->create();
        $adminRole = Role::findByName('Admin');
        $adminRole->givePermissionTo('users.view');

        $user->assignRole('Admin');

        $this->assertFalse($user->isSuperAdmin());
        $this->assertTrue($user->hasPermission('users.view'));
        $this->assertFalse($user->hasPermission('users.create'));
    }

    public function test_user_without_role_has_no_permissions(): void
    {
        $user = User::factory()->create();

        $this->assertFalse($user->isSuperAdmin());
        $this->assertFalse($user->hasPermission('users.view'));
        $this->assertFalse($user->hasPermission('users.create'));
    }

    public function test_permission_service_works(): void
    {
        $permissionService = app(\App\Services\PermissionService::class);
        $user = User::factory()->create();
        $adminRole = Role::findByName('Admin');
        $adminRole->givePermissionTo('users.view');

        $user->assignRole('Admin');

        $this->assertTrue($permissionService->userHasPermission($user, 'users.view'));
        $this->assertFalse($permissionService->userHasPermission($user, 'users.create'));
    }

    public function test_helper_functions_work(): void
    {
        $user = User::factory()->create();
        $user->assignRole('Super Admin');

        $this->actingAs($user);

        $this->assertTrue(userCan('users.view'));
        $this->assertTrue(isSuperAdmin());
        $this->assertTrue(userHasRole('Super Admin'));
    }
}
