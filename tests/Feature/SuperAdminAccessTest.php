<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class SuperAdminAccessTest extends TestCase
{
    use RefreshDatabase;

    protected $superAdmin;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create permissions
        $permissions = [
            'documents.view', 'documents.create', 'documents.edit', 'documents.delete',
            'litigants.view', 'litigants.create', 'litigants.edit', 'litigants.delete',
            'users.view', 'users.create', 'users.edit', 'users.delete',
        ];
        
        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission, 'guard_name' => 'web']);
        }
        
        // Create Super Admin role
        $superAdminRole = Role::create(['name' => 'Super Admin', 'guard_name' => 'web']);
        
        // Create Super Admin user
        $this->superAdmin = User::factory()->create([
            'email' => '<EMAIL>',
            'name' => 'Super Administrator'
        ]);
        
        $this->superAdmin->assignRole($superAdminRole);
    }

    public function test_super_admin_can_access_documents_index(): void
    {
        $response = $this->actingAs($this->superAdmin)->get('/documents');
        
        $this->assertEquals(200, $response->getStatusCode());
    }

    public function test_super_admin_can_access_documents_wizard(): void
    {
        $response = $this->actingAs($this->superAdmin)->get('/documents/wizard');
        
        $this->assertEquals(200, $response->getStatusCode());
    }

    public function test_super_admin_can_access_litigants(): void
    {
        $response = $this->actingAs($this->superAdmin)->get('/litigants');
        
        $this->assertEquals(200, $response->getStatusCode());
    }

    public function test_super_admin_bypasses_all_permissions(): void
    {
        $this->assertTrue($this->superAdmin->isSuperAdmin());
        $this->assertTrue($this->superAdmin->hasPermission('documents.view'));
        $this->assertTrue($this->superAdmin->hasPermission('users.create'));
        $this->assertTrue($this->superAdmin->hasPermission('non.existent.permission'));
    }

    public function test_super_admin_helper_functions(): void
    {
        $this->actingAs($this->superAdmin);
        
        $this->assertTrue(userCan('documents.view'));
        $this->assertTrue(userCan('any.permission'));
        $this->assertTrue(isSuperAdmin());
        $this->assertTrue(userHasRole('Super Admin'));
    }

    public function test_regular_user_cannot_access_without_permissions(): void
    {
        $regularUser = User::factory()->create();
        
        $response = $this->actingAs($regularUser)->get('/documents');
        $this->assertEquals(403, $response->getStatusCode());
        
        $response = $this->actingAs($regularUser)->get('/litigants');
        $this->assertEquals(403, $response->getStatusCode());
    }

    public function test_unauthenticated_user_redirected(): void
    {
        $response = $this->get('/documents');
        $this->assertEquals(302, $response->getStatusCode());
        
        $response = $this->get('/litigants');
        $this->assertEquals(302, $response->getStatusCode());
    }
}
