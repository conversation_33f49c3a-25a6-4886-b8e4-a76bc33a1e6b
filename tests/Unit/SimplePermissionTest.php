<?php

namespace Tests\Unit;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class SimplePermissionTest extends TestCase
{
    use RefreshDatabase;

    public function test_config_acl_exists(): void
    {
        $config = config('acl');
        $this->assertIsArray($config);
        $this->assertArrayHasKey('modules', $config);
        $this->assertArrayHasKey('super_admin_role', $config);
    }

    public function test_permission_service_can_be_resolved(): void
    {
        $service = app(\App\Services\PermissionService::class);
        $this->assertInstanceOf(\App\Services\PermissionService::class, $service);
    }

    public function test_helper_functions_exist(): void
    {
        $this->assertTrue(function_exists('userCan'));
        $this->assertTrue(function_exists('isSuperAdmin'));
        $this->assertTrue(function_exists('userHasRole'));
    }
}
